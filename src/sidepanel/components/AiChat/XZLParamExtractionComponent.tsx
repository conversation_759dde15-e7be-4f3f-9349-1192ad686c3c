import React, { useState, useEffect } from 'react';
import { Button, message, Table, Spin, Alert, Typography, Card, Collapse, List, Input, Switch, Modal } from 'antd';
import CustomMarkdown from '../../../common/components/CustomMarkdown';

interface XZLParamExtractionComponentProps {
  navbar?: any;
  xzlParamData: any
}

const XZLParamExtractionComponent: React.FC<XZLParamExtractionComponentProps> = (props) => {
  const { navbar, xzlParamData } = props;
  const [extractionResult, setExtractionResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [activeKeys, setActiveKeys] = useState<string[]>([]); // 搜索数据列表的展开状态
  const [activeLoopKeys, setActiveLoopKeys] = useState<string[]>([]); // 循环列表的展开状态
  const [activeHistoryKeys, setActiveHistoryKeys] = useState<string[]>([]); // 历史对话的展开状态
  const [searchKeyword, setSearchKeyword] = useState(''); // 添加搜索关键词状态
  const [aiAnalysis, setAiAnalysis] = useState<boolean>(false); // 添加 aiAnalysis 状态，默认为 true

  // 当 analysisResult 获取到值时，设置所有折叠面板默认展开
  useEffect(() => {
    if (analysisResult) {
      // 设置搜索数据列表默认全部展开
      const allKeys = analysisResult.searchDataList?.map((_: any, index: number) => index.toString()) || [];
      setActiveKeys(allKeys);

      // 设置循环列表默认全部展开
      const allLoopKeys = analysisResult.loopList?.map((_: any, index: number) => index.toString()) || [];
      setActiveLoopKeys(allLoopKeys);

      // // 设置历史对话默认全部展开
      // const allHistoryKeys = analysisResult.historyList?.map((_: any, index: number) => index.toString()) || [];
      // setActiveHistoryKeys(allHistoryKeys);
    }
  }, [analysisResult]);

  // 当 dataSource 获取到值时，将 loading 设置为 false
  useEffect(() => {
    // 将对象转换为表格数据
    const dataSource = xzlParamData?.extractedFields ?
      Object.entries(xzlParamData.extractedFields).map(([key, value], index) => ({
        key: index,
        fieldName: key,
        fieldValue: typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value),
      })) : [];

    if (dataSource.length > 0) {
      setLoading(false);
    }
  }, [xzlParamData]);

  const handleExtractParams = async () => {
    setLoading(true);
    try {
      // 发送消息到background触发参数提取
      const response: any = await chrome.runtime.sendMessage({
        type: 'XZL_PARAM_EXTRACTION_REQUEST_FROM_SIDEPANEL'
      });

      if (response && response.success) {
        setExtractionResult(response);
        message.success('参数提取成功');
      } else {
        setExtractionResult(response);
        console.error(response?.error || '参数提取失败');

      }
    } catch (error) {
      console.error('参数提取失败:', error);
      const errorMessage = (error as Error).message;
      // 不显示'document is not defined'错误
      if (!errorMessage.includes('document is not defined')) {
        message.error('参数提取失败: ' + errorMessage);
      }
    }
  };

  const handleAnalyze = async () => {
    setAnalyzing(true);
    setAnalysisResult(null);
    try {
      // 获取当前激活的标签页
      const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!activeTab || !activeTab.url) {
        message.error('无法获取当前页面信息');
        setAnalyzing(false);
        return;
      }

      // 从当前激活页面获取 cookie
      const cookies = await chrome.cookies.getAll({ url: activeTab.url });
      const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

      // 参考 extractTraceId() 方法获取 traceId
      // 注入脚本到当前页面以获取 traceId
      const traceIdResult = await chrome.scripting.executeScript({
        target: { tabId: activeTab.id! },
        func: () => {
          try {
            // 查找所有包含指定 class 的元素
            const labelElements = document.querySelectorAll('.sprite-descriptions-item-label');

            // 遍历元素，查找标签为"traceId"的元素
            for (let i = 0; i < labelElements.length; i++) {
              const labelElement = labelElements[i];
              const labelContent = labelElement.textContent?.trim();

              // 检查标签是否为"traceId"
              if (labelContent && labelContent === 'traceId') {
                // 获取相邻的content元素
                const contentElement = labelElement.nextElementSibling;
                if (contentElement && contentElement.classList.contains('sprite-descriptions-item-content')) {
                  const traceId = contentElement.textContent?.trim();
                  if (traceId) {
                    return { success: true, traceId };
                  }
                }
              }
            }

            return { success: false, error: '未找到 traceId' };
          } catch (error) {
            return { success: false, error: `提取 traceId 失败: ${error}` };
          }
        }
      });

      const traceIdResponse = traceIdResult[0];
      if (!traceIdResponse.result?.success || !traceIdResponse.result?.traceId) {
        message.error(traceIdResponse.result?.error || '获取 traceId 失败');
        setAnalyzing(false);
        return;
      }

      const traceId = traceIdResponse.result.traceId;

      // 发送 POST 请求到指定 URL
      const response = await fetch('http://n8n.fe.htsc/webhook/zhengu-annotation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          Cookie: cookieString,
          // traceId: 'light-9a8aff2c-23b2-4b68-be26-7c33b1a9e367',
          traceId,
          aiAnalysis, // 添加 aiAnalysis 参数
        })
      });

      if (response.ok) {
        const result = await response.json();
        result.traceId = traceId;
        setAnalysisResult(result);
        console.log('打标分析结果:', result);
        message.success('打标分析请求已发送');
      } else {
        message.error('打标分析请求失败');
      }
    } catch (error) {
      console.error('打标分析失败:', error);
      message.error('打标分析失败: ' + (error as Error).message);
    } finally {
      setAnalyzing(false);
    }
  };

  // 添加一键标记无问题函数
  const handleMarkNoProblem = async () => {
    try {
      // 获取当前激活的标签页
      const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!activeTab || !activeTab.url) {
        message.error('无法获取当前页面信息');
        return;
      }

      // 从当前激活页面获取 cookie
      const cookies = await chrome.cookies.getAll({ url: activeTab.url });
      const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

      // 发送 POST 请求到指定 URL
      const response = await fetch('http://n8n.fe.htsc/webhook/zhengu/end', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          traceId: analysisResult.traceId,
          Cookie: cookieString,
        })
      });

      if (response.ok) {
        message.success('标记无问题成功');
      } else {
        message.error('标记无问题失败');
      }
    } catch (error) {
      console.error('标记无问题失败:', error);
      message.error('标记无问题失败: ' + (error as Error).message);
    }
  };

  // 确认标记无问题
  const confirmMarkNoProblem = () => {
    Modal.confirm({
      title: '确认标记',
      content: '确定要标记为无问题吗？此操作不可撤销。',
      okText: '确认',
      cancelText: '取消',
      onOk: handleMarkNoProblem,
    });
  };

  // 添加一键填入标签函数
  const handleFillInLabel = async () => {
    try {
      // 获取当前激活的标签页
      const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!activeTab || !activeTab.url) {
        message.error('无法获取当前页面信息');
        return;
      }

      // 从当前激活页面获取 cookie
      const cookies = await chrome.cookies.getAll({ url: activeTab.url });
      const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

      // 发送 POST 请求到指定 URL
      const response = await fetch('http://n8n.fe.htsc/webhook/zhengu/fill-in', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          traceId: analysisResult.traceId,
          Cookie: cookieString,
          labelRemark: analysisResult.aiAnalysis.labelRemark,
          labelClusterRemark: analysisResult.aiAnalysis.clusterLabel
        })
      });

      if (response.ok) {
        message.success('标签填入成功');
      } else {
        message.error('标签填入失败');
      }
    } catch (error) {
      console.error('标签填入失败:', error);
      message.error('标签填入失败: ' + (error as Error).message);
    }
  };

  // 确认填入标签
  const confirmFillInLabel = () => {
    const labelRemark = analysisResult?.aiAnalysis?.labelRemark || '';
    const labelClusterRemark = analysisResult?.aiAnalysis?.clusterLabel || '';

    Modal.confirm({
      title: '确认填入标签',
      content: (
        <div>
          <p>确定要将AI分析结果填入标签吗？此操作不可撤销。</p>
          {labelRemark && (
            <p><strong>标签备注:</strong> {labelRemark}</p>
          )}
          {labelClusterRemark && (
            <p><strong>聚类标签:</strong> {labelClusterRemark}</p>
          )}
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: handleFillInLabel,
    });
  };

  // 将对象转换为表格数据
  const dataSource = xzlParamData?.extractedFields ?
    Object.entries(xzlParamData.extractedFields).map(([key, value], index) => ({
      key: index,
      fieldName: key,
      fieldValue: typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value),
    })) : [];

  const columns = [
    {
      title: '字段路径',
      dataIndex: 'fieldName',
      key: 'fieldName',
      width: 60,
    },
    {
      title: '字段值',
      dataIndex: 'fieldValue',
      key: 'fieldValue',
      render: (text: string) => {
        // 预处理文本，将特殊协议链接转换为纯文本
        const processedText = text ? text.replace(/\[([^\]]+)\]\(http:\/\/action:(\d+)([^\)]*)\)/g, '$1') : '';

        console.log('Rendering markdown text:', text, 'Processed:', processedText);

        return (
          <CustomMarkdown>
            {processedText || ''}
          </CustomMarkdown>
        );
      }
    },
  ];

  // 创建高亮文本组件
  const HighlightText = ({ text, keyword }: { text: string; keyword: string }) => {
    if (!keyword.trim() || !text) {
      return <span>{text}</span>;
    }

    try {
      const parts = text.split(new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi'));

      return (
        <span>
          {parts.map((part, index) =>
            part.toLowerCase() === keyword.toLowerCase() ? (
              <span key={index} style={{ backgroundColor: '#ffecb3', fontWeight: 'bold' }}>{part}</span>
            ) : (
              part
            )
          )}
        </span>
      );
    } catch (e) {
      // 如果正则表达式出错，直接返回原文本
      return <span>{text}</span>;
    }
  };

  // 修改 ReactMarkdown 组件以支持高亮
  const HighlightedMarkdown = ({ children }: { children: string }) => {
    // 添加对 children 为 null 或 undefined 的检查
    if (children === null || children === undefined) {
      return <CustomMarkdown>{''}</CustomMarkdown>;
    }

    // 预处理文本，将特殊协议链接转换为纯文本
    const processedChildren = children ? children.replace(/\[([^\]]+)\]\(http:\/\/action:(\d+)([^\)]*)\)/g, '$1') : '';
    console.log('HighlightedMarkdown children:', children, 'Processed:', processedChildren);

    return (
      <CustomMarkdown searchKeyword={searchKeyword}>
        {processedChildren}
      </CustomMarkdown>
    );
  };

  // 渲染数组格式的结果（增加高亮支持）
  const renderArrayResult = (dataArray: any[], keyword: string) => {
    return (
      <List
        dataSource={dataArray}
        renderItem={(item: any, index: number) => (
          <List.Item key={index}>
            <Card size="small" style={{ width: '100%' }}>
              <div style={{ marginBottom: 8 }}>
                <strong>
                  {keyword ?
                    <HighlightText text={item.文章标题 || `项目 ${index + 1}`} keyword={keyword} /> :
                    (item.文章标题 || `项目 ${index + 1}`)
                  }
                </strong>
              </div>
              <div style={{ marginBottom: 4 }}>
                <small>
                  {item.文章来源 && (
                    <span>
                      来源: {keyword ? <HighlightText text={item.文章来源} keyword={keyword} /> : item.文章来源}
                    </span>
                  )}
                  {item.文章类型 && (
                    <span style={{ marginLeft: 12 }}>
                      类型: {keyword ? <HighlightText text={item.文章类型} keyword={keyword} /> : item.文章类型}
                    </span>
                  )}
                  {item.文章发布时间 && (
                    <span style={{ marginLeft: 12 }}>
                      发布时间: {keyword ? <HighlightText text={item.文章发布时间} keyword={keyword} /> : item.文章发布时间}
                    </span>
                  )}
                </small>
              </div>
              <div>
                <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                  {keyword ?
                    <HighlightText
                      text={item.文章片段内容 || item.content || JSON.stringify(item, null, 2)}
                      keyword={keyword}
                    /> :
                    (item.文章片段内容 || item.content || JSON.stringify(item, null, 2))
                  }
                </pre>
              </div>
            </Card>
          </List.Item>
        )}
      />
    );
  };

  const renderAnalysisResult = () => {
    if (!analysisResult) return null;

    // 处理折叠面板的展开/收起
    const handleToggleAll = () => {
      if (activeKeys.length === (analysisResult.searchDataList?.length || 0)) {
        // 如果全部展开，则收起所有
        setActiveKeys([]);
      } else {
        // 如果部分展开或全部收起，则展开所有
        const allKeys = analysisResult.searchDataList?.map((_: any, index: number) => index.toString()) || [];
        setActiveKeys(allKeys);
      }
    };

    // 处理循环列表的展开/收起
    const handleToggleAllLoops = () => {
      if (activeLoopKeys.length === (analysisResult.loopList?.length || 0)) {
        // 如果全部展开，则收起所有
        setActiveLoopKeys([]);
      } else {
        // 如果部分展开或全部收起，则展开所有
        const allKeys = analysisResult.loopList?.map((_: any, index: number) => index.toString()) || [];
        setActiveLoopKeys(allKeys);
      }
    };

    // 处理循环列表中搜索数据的一键展开/收起
    const handleToggleLoopSearchData = (loopIndex: number, searchDataList: any[]) => {
      const currentLoopSearchKeys = activeKeys.filter(key => key.startsWith(`${loopIndex}-`));

      let newActiveKeys = [...activeKeys];

      if (currentLoopSearchKeys.length === searchDataList.length) {
        // 如果当前循环的所有搜索数据都展开了，则收起它们
        newActiveKeys = activeKeys.filter(key => !key.startsWith(`${loopIndex}-`));
      } else {
        // 否则展开所有当前循环的搜索数据
        const loopSearchKeys = searchDataList.map((_, index) => `${loopIndex}-${index}`);
        newActiveKeys = [...activeKeys.filter(key => !key.startsWith(`${loopIndex}-`)), ...loopSearchKeys];
      }

      setActiveKeys(newActiveKeys);
    };

    // 添加处理历史对话展开/收起的函数
    const handleToggleAllHistory = () => {
      if (activeHistoryKeys.length === (analysisResult.historyList?.length || 0)) {
        // 如果全部展开，则收起所有
        setActiveHistoryKeys([]);
      } else {
        // 如果部分展开或全部收起，则展开所有
        const allKeys = analysisResult.historyList?.map((_: any, index: number) => index.toString()) || [];
        setActiveHistoryKeys(allKeys);
      }
    };

    return (
      <Card size="small" style={{ marginTop: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Typography.Title level={4} style={{ margin: 0 }}>分析结果（仅供参考）</Typography.Title>
          <Input.Search
            placeholder="在分析结果中高亮关键字"
            onSearch={(value) => setSearchKeyword(value)}
            onChange={(e) => setSearchKeyword(e.target.value)}
            value={searchKeyword}
            style={{ width: 250 }}
            allowClear
          />
        </div>

        {analysisResult.aiAnalysis && (
          <div>
            {/* <Typography.Title level={5}>AI 分析</Typography.Title> */}
            <div style={{ paddingLeft: 16 }}>
              <p>
                <strong>是否有问题:</strong> {searchKeyword ?
                  <HighlightText text={analysisResult.aiAnalysis.hasProblem ? '是' : '否'} keyword={searchKeyword} /> :
                  (analysisResult.aiAnalysis.hasProblem ? '是' : '否')
                }
              </p>
              {analysisResult.aiAnalysis.clusterLabel && (
                <p>
                  <strong>聚类标签:</strong> {searchKeyword ?
                    <HighlightText text={analysisResult.aiAnalysis.clusterLabel} keyword={searchKeyword} /> :
                    analysisResult.aiAnalysis.clusterLabel
                  }
                </p>
              )}
              {analysisResult.aiAnalysis.labelRemark && (
                <p>
                  <strong>标签备注:</strong> {searchKeyword ?
                    <HighlightText text={analysisResult.aiAnalysis.labelRemark} keyword={searchKeyword} /> :
                    analysisResult.aiAnalysis.labelRemark
                  }
                </p>
              )}
              {analysisResult.aiAnalysis.reason && (
                <p>
                  <strong>原因:</strong> {searchKeyword ?
                    <HighlightText text={analysisResult.aiAnalysis.reason} keyword={searchKeyword} /> :
                    analysisResult.aiAnalysis.reason
                  }
                </p>
              )}

              {/* 添加一键填入标签按钮，仅在开启AI分析时显示 */}
              {/* {aiAnalysis && (
                <div style={{ marginTop: 16 }}>
                  <Button
                    type="primary"
                    onClick={confirmFillInLabel}
                    size="small"
                  >
                    一键填入标签
                  </Button>
                </div>
              )} */}
            </div>
          </div>
        )}
        <hr></hr>
        <Typography.Title level={4} style={{ margin: 0 }}>分析过程</Typography.Title>
        {analysisResult.userInput && (
          <div style={{ marginBottom: 16 }}>
            <strong>用户输入:</strong>
            <p>
              {searchKeyword ?
                <HighlightText text={analysisResult.userInput} keyword={searchKeyword} /> :
                analysisResult.userInput
              }
            </p>
          </div>
        )}
        {analysisResult.userFeedback && (
          <div style={{ marginBottom: 16 }}>
            <strong>用户反馈:</strong>
            <p>
              {searchKeyword ?
                <HighlightText text={analysisResult.userFeedback} keyword={searchKeyword} /> :
                analysisResult.userFeedback
              }
            </p>
          </div>
        )}

        {analysisResult.evaluationRemark && (
          <div style={{ marginBottom: 16 }}>
            <strong>测评反馈备注</strong>
            <Card size="small">
              <HighlightedMarkdown>
                {analysisResult.evaluationRemark || ''}
              </HighlightedMarkdown>
            </Card>
          </div>
        )}
        {analysisResult.output && (
          <div style={{ marginBottom: 16 }}>
            <strong>输出</strong>
            <Card size="small">
              <HighlightedMarkdown>
                {analysisResult.output || ''}
              </HighlightedMarkdown>
              {analysisResult.references && analysisResult.references.length > 0 && (
                <div style={{ marginBottom: 8, backgroundColor: '#fafafa', padding: 8 }}>
                  <strong>引用</strong>

                  {analysisResult.references.map((ref: any, index: number) => (
                    <div key={index} style={{ fontSize: '12px', marginBottom: '4px' }}>
                      {ref.pubTime} | {ref.type} | {ref.source} | {ref.title}
                    </div>
                  ))}

                </div>
              )}
            </Card>
          </div>
        )}



        {analysisResult.message && (
          <Alert
            message={searchKeyword ?
              <HighlightText text={analysisResult.message} keyword={searchKeyword} /> :
              analysisResult.message
            }
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {analysisResult.allInfoResult && (
          <div style={{ marginBottom: 16 }}>
            <strong>All Info Result(根据上下文总结问题)</strong>
            <ul>
              {analysisResult.allInfoResult.map((item: string, index: number) => (
                <li key={index}>
                  {searchKeyword ?
                    <HighlightText text={item} keyword={searchKeyword} /> :
                    item
                  }
                </li>
              ))}
            </ul>
          </div>
        )}

        {analysisResult.historyList && analysisResult.historyList.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <strong>历史对话</strong>
              <Button size="small" onClick={handleToggleAllHistory}>
                {activeHistoryKeys.length === analysisResult.historyList.length ? '全部收起' : '一键展开'}
              </Button>
            </div>
            <Collapse activeKey={activeHistoryKeys} onChange={(keys) => setActiveHistoryKeys(keys as string[])}>
              {analysisResult.historyList.map((item: any, index: number) => {
                // 添加检查确保 item 存在且不是 null 或 undefined
                if (!item) {
                  return (
                    <Collapse.Panel
                      header={`记录 ${index + 1}: 无有效数据`}
                      key={index}
                    >
                      <p>无效的历史记录条目</p>
                    </Collapse.Panel>
                  );
                }

                return (
                  <Collapse.Panel
                    header={searchKeyword ?
                      <HighlightText text={`记录 ${index + 1}: ${item.query || '无查询信息'}`} keyword={searchKeyword} /> :
                      `记录 ${index + 1}: ${item.query || '无查询信息'}`
                    }
                    key={index}
                  >
                    <div style={{ marginBottom: 12 }}>
                      <strong>查询</strong>
                      <Card size="small">
                        <p>
                          {searchKeyword ?
                            <HighlightText text={item.query || '无查询信息'} keyword={searchKeyword} /> :
                            (item.query || '无查询信息')
                          }
                        </p>
                      </Card>
                    </div>

                    {item.answer && (
                      <div>
                        <strong>回答</strong>
                        <Card size="small">
                          <HighlightedMarkdown>
                            {item.answer || ''}
                          </HighlightedMarkdown>
                        </Card>
                      </div>
                    )}
                  </Collapse.Panel>
                );
              })}
            </Collapse>
          </div>
        )}
        {analysisResult.CoT && (
          <div style={{ marginBottom: 16 }}>
            <strong>Chain of Thought（思维链）</strong>
            <Card size="small">
              <HighlightedMarkdown>
                {analysisResult.CoT || ''}
              </HighlightedMarkdown>
            </Card>
          </div>
        )}

        {analysisResult.toolset && (
          <div style={{ marginBottom: 16 }}>
            <strong>Toolset（工具拆解）</strong>
            <Card size="small">
              <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                {JSON.stringify(analysisResult.toolset, null, 2)}
              </pre>
            </Card>
          </div>
        )}

        {analysisResult.loopList && analysisResult.loopList.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <strong style={{ margin: 0 }}>多意图循环列表</strong>
              <Button size="small" onClick={handleToggleAllLoops}>
                {activeLoopKeys.length === analysisResult.loopList.length ? '全部收起' : '一键展开'}
              </Button>
            </div>
            <Collapse activeKey={activeLoopKeys} onChange={(keys) => setActiveLoopKeys(keys as string[])}>
              {analysisResult.loopList.map((loopItem: any, loopIndex: number) => (
                <Collapse.Panel
                  header={searchKeyword ?
                    <HighlightText text={`${loopItem.index}`} keyword={searchKeyword} /> :
                    `${loopItem.index}`
                  }
                  key={loopIndex}
                >
                  {loopItem.result && (
                    <div>
                      {loopItem.result.allInfoResult && (
                        <div style={{ marginBottom: 12 }}>
                          <strong>All Info Result</strong>
                          <ul>
                            {loopItem.result.allInfoResult.map((item: string, index: number) => (
                              <li key={index}>
                                {searchKeyword ?
                                  <HighlightText text={item} keyword={searchKeyword} /> :
                                  item
                                }
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {loopItem.result.CoT && (
                        <div style={{ marginBottom: 12 }}>
                          <strong>Chain of Thought</strong>
                          <Card size="small">
                            <HighlightedMarkdown >
                              {loopItem.result.CoT || ''}
                            </HighlightedMarkdown>
                          </Card>
                        </div>
                      )}

                      {loopItem.result.searchDataList && loopItem.result.searchDataList.length > 0 && (
                        <div>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                            <strong style={{ margin: 0 }}>指标查询/联网搜索结果</strong>
                            <Button
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleToggleLoopSearchData(loopIndex, loopItem.result.searchDataList);
                              }}
                            >
                              {activeKeys.filter(key => key.startsWith(`${loopIndex}-`)).length === loopItem.result.searchDataList.length ? '全部收起' : '一键展开'}
                            </Button>
                          </div>
                          <Collapse activeKey={
                            activeKeys.filter(key => key.startsWith(`${loopIndex}-`)).map(key => key.substring(key.indexOf('-') + 1))
                          } onChange={(keys) => {
                            // 处理嵌套面板的展开/收起
                            const otherKeys = activeKeys.filter(key => !key.startsWith(`${loopIndex}-`));
                            const newKeys = (Array.isArray(keys) ? keys : [keys]).map(key => `${loopIndex}-${key}`);
                            setActiveKeys([...otherKeys, ...newKeys]);
                          }}>
                            {loopItem.result.searchDataList.map((item: any, index: number) => (
                              <Collapse.Panel
                                header={searchKeyword ?
                                  <HighlightText text={`${item.type || '未知类型'}: ${item.query || '无查询信息'}`} keyword={searchKeyword} /> :
                                  `${item.type || '未知类型'}: ${item.query || '无查询信息'}`
                                }
                                key={index}
                              >
                                {item.result ? (
                                  <div>
                                    {Array.isArray(item.result) ? (
                                      renderArrayResult(item.result, searchKeyword)
                                    ) : (
                                      <Card size="small" style={{ marginTop: 8 }}>
                                        <HighlightedMarkdown>
                                          {String(item.result || '')}
                                        </HighlightedMarkdown>
                                      </Card>
                                    )}
                                  </div>
                                ) : (
                                  <p>无结果数据</p>
                                )}
                              </Collapse.Panel>
                            ))}
                          </Collapse>
                        </div>
                      )}
                    </div>
                  )}
                </Collapse.Panel>
              ))}
            </Collapse>
          </div>
        )}

        {analysisResult.searchDataList && analysisResult.searchDataList.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <strong style={{ margin: 0 }}>指标查询/联网搜索结果</strong>
              <Button size="small" onClick={handleToggleAll}>
                {activeKeys.length === analysisResult.searchDataList.length ? '全部收起' : '一键展开'}
              </Button>
            </div>
            <Collapse activeKey={activeKeys} onChange={(keys) => setActiveKeys(keys as string[])}>
              {analysisResult.searchDataList.map((item: any, index: number) => (
                <Collapse.Panel
                  header={searchKeyword ?
                    <HighlightText text={`${item.type || '未知类型'}: ${item.query || '无查询信息'}`} keyword={searchKeyword} /> :
                    `${item.type || '未知类型'}: ${item.query || '无查询信息'}`
                  }
                  key={index}
                >
                  {item.result ? (
                    <div>
                      {/* 检查 result 是否为数组 */}
                      {Array.isArray(item.result) ? (
                        renderArrayResult(item.result, searchKeyword)
                      ) : (
                        <Card size="small" style={{ marginTop: 8 }}>
                          <HighlightedMarkdown>
                            {String(item.result || '')}
                          </HighlightedMarkdown>
                        </Card>
                      )}
                    </div>
                  ) : (
                    <p>无结果数据</p>
                  )}
                </Collapse.Panel>
              ))}
            </Collapse>
          </div>
        )}

      </Card>
    );
  };

  return (
    <div style={{ padding: '16px', flex: 1, overflow: 'auto' }}>
      {/* <Navbar {...navbar} /> */}
      <div style={{ marginBottom: '16px' }}>
        <strong>新涨乐评测工具</strong>
        <p>点击参数提取可以通过用户配置提取节点信息。</p>
        <p>点击诊股分析，提取诊股所需信息，开启AI分析，会使用 ai 进行诊股分析，如果只需要诊股信息，请关闭。</p>
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center', flexWrap: 'wrap' }}>
          <Button
            type="primary"
            onClick={handleExtractParams}
            disabled={loading}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minWidth: '120px'
            }}
          >
            {loading ? (
              <>
                <Spin size="small" style={{ marginRight: 8 }} />
                <span>提取中...</span>
              </>
            ) : (
              '提取参数'
            )}
          </Button>

          <Button
            type="primary"
            onClick={handleAnalyze}
            disabled={analyzing}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minWidth: '120px'
            }}
          >
            {analyzing ? (
              <>
                <Spin size="small" style={{ marginRight: 8 }} />
                <span>分析中...</span>
              </>
            ) : (
              '诊股分析'
            )}
          </Button>

          {/* 添加一键标记无问题按钮 */}
          {analysisResult?.traceId ? <Button
            onClick={confirmMarkNoProblem}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minWidth: '160px'
            }}
          >
            一键标记无问题
          </Button> : null}

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span>开启AI分析:</span>
            <Switch
              checked={aiAnalysis}
              onChange={setAiAnalysis}
              checkedChildren="开"
              unCheckedChildren="关"
            />
          </div>
        </div>

        {renderAnalysisResult()}
      </div>

      {/* 显示提取结果 */}
      {xzlParamData && xzlParamData.success && xzlParamData.extractedFields && (
        <div style={{ padding: '16px' }}>
          <h3>新涨乐标注参数提取结果</h3>
          <Table
            dataSource={dataSource}
            columns={columns}
            pagination={false}
            scroll={{ y: 400 }}
            size="small"
          />
        </div>
      )}
    </div>
  );
};

export default XZLParamExtractionComponent;