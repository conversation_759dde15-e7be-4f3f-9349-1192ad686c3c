import { useState, useEffect } from 'react'
import {
  Button,
  Badge,
  Modal,
  Form,
  Input,
  Radio,
  Upload,
  message,
  Tooltip
} from 'antd/es'
import {
  PlusOutlined,
  LeftOutlined,
  InfoCircleFilled,
} from '@ant-design/icons'
import { ImgIcon } from '@/src/common/Icons'
import {
  saveUserSkills,
  getUserId,
  selectUserKills,
  optimizePrompt,
  deleteUserKills,
  type SaveUserSkillsParams,
} from '@/src/common/utils/userConfigApi'
import { createSkillConfig } from '@/src/config/aiChatConfig'
import images from '@/src/common/skillIcon/icon'
import logo from '@/src/common/skillIcon/marketLogo.png'
import deleteIcon from '@/src/common/skillIcon/deleteIcon.png'
import addIcon from '@/src/common/skillIcon/addIcon.png'
import spaceIcon from '@/src/common/skillIcon/spaceIocn.png'
import marketSkill from '@/src/common/skillIcon/marketSkill.png'
import prompt from '@/src/common/skillIcon/prompt.png'
import promptImg from '@/src/common/skillIcon/loading.gif'

import './skillsmarket.less'
// 生成随机图标ID
const getRandomIcon = () => {
  // 获取images对象的所有键
  const imageKeys = Object.keys(images);
  // 随机选择一个键
  const randomIndex = Math.floor(Math.random() * imageKeys.length);
  return imageKeys[randomIndex];
}
export default () => {
  const [form] = Form.useForm()
  const [activeTab, setActiveTab] = useState('market')
  const [hasSkills, setHasSkills] = useState([])
  const [noSkills, setNoSkills] = useState([])
  const [open, setOpen] = useState(false)
  const [id, setId] = useState<string>()
  const [fileId, setFileId] = useState<string>('')
  const [tempFileId, setTempFileId] = useState<string>(getRandomIcon())

  const [loading, setLoading] = useState(false)

  const [openIcon, setOpenIcon] = useState(false)
  const [promptLoading, setPromptLoading] = useState(false)
  const [record, setRecord] = useState<SaveUserSkillsParams>()
  const [deleteOpen, setDeleteOpen] = useState(false)

  // 获取用户技能配置数据
  const fetchData = async () => {
    try {
      const userId = await getUserId()
      const response = await selectUserKills({ createUserId: userId })
      setId(userId || '')
      // 确保响应是成功的
      if (
        !response ||
        typeof response !== 'object' ||
        'code' in response === false
      ) {
        throw new Error('无效的API响应')
      }

      if (response.code !== '0') {
        throw new Error(response.msg || '获取配置失败')
      }
      const config = response.resultData || []
      const commonSkills = config.filter((item) => item.isConfig === 0)
      const selectedSkills = config.filter((item) => item.isConfig === 1)
      setHasSkills(selectedSkills)
      setNoSkills(commonSkills)
    } catch (error) {
    } finally {
    }
  }
  useEffect(() => {
    fetchData()
  }, [])

  // 通用技能状态更新函数
  const updateSkillStatus = async (skill, isConfig, prevHasSkills, prevNoSkills) => {
    try {
      const params = {
        ...skill,
        isConfig, // 1表示已配置，0表示未配置
      };

      const response = await saveUserSkills({ skillConfigInfos: [params] });

      if (!response || typeof response !== 'object' || 'code' in response === false) {
        throw new Error('无效的API响应');
      }

      if (response.code !== '0') {
        message.error(response.msg || (isConfig ? '添加技能失败' : '移除技能失败'));
        return false;
      }

      return true;
    } catch (error) {
      message.error((isConfig ? '添加技能失败: ' : '移除技能失败: ') + error.message);
      return false;
    }
  };

  // 添加技能到已选列表
  const handleAddSkill = async (skill) => {
    setNoSkills((prev) => prev.filter((item) => item.id !== skill.id));
    setHasSkills((prev) => [...prev, skill]);

    // 实时同步到后台
    const success = await updateSkillStatus(skill, 1, hasSkills, noSkills);

    // 如果保存失败，恢复状态
    if (!success) {
      setHasSkills((prev) => prev.filter((item) => item.id !== skill.id));
      setNoSkills((prev) => [...prev, skill]);
    }
  };

  // 从已选列表中移除技能
  const handleRemoveSkill = async (skill) => {
    setHasSkills((prev) => prev.filter((item) => item.id !== skill.id));
    setNoSkills((prev) => [...prev, skill]);

    // 实时同步到后台
    const success = await updateSkillStatus(skill, 0, hasSkills, noSkills);

    // 如果保存失败，恢复状态
    if (!success) {
      setNoSkills((prev) => prev.filter((item) => item.id !== skill.id));
      setHasSkills((prev) => [...prev, skill]);
    }
  }

  // 渲染技能市场主界面
  const renderAccountTab = () => {
    return (
      <div className="market">
        <div className="market-header">
          <span className="header-title">技能市场</span>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            style={{
              height: 32,
              borderRadius: 4,
              backgroundColor: '#1677FF',
              fontSize: 14,
              lineHeight: '22px',
            }}
            onClick={() => setOpen(true)}
          >
            新建技能
          </Button>
        </div>
        <div className="skills">通用技能</div>
        <div className="common-skills">
          {createSkillConfig().map((item, index) => {
            return (
              <div className="skill-item" key={item.label}>
                <div>{item.icon}</div>
                <div className="skill-item-title">{item.label}</div>
              </div>
            )
          })}
        </div>
        <div className="skills">
          已选配技能
          <Tooltip title="已选配技能将在对话框上方技能列表中展示">
            <InfoCircleFilled style={{ fontSize: '16px', marginLeft: '4px', color: '#393737' }} />
          </Tooltip>
        </div>
        <div className="common-skills">
          {hasSkills.length > 0 ? (
            hasSkills.map((item, index) => {
              return (
                <a href="#" key={item.id}>
                  <Badge
                    count={
                      <>
                        <img
                          src={deleteIcon}
                          alt=""
                          className="skill-badge"
                          onClick={(e) => {
                            e.preventDefault()
                            e.stopPropagation()
                            handleRemoveSkill(item)
                          }}
                        />
                      </>
                    }
                  >
                    <>
                      <div className="skill-item">
                        <div>
                          <ImgIcon src={images[item.fileId]} />
                        </div>
                        <div className="skill-item-title">{item.name}</div>
                      </div>
                      {renderAction(item)}
                    </>
                  </Badge>
                </a>
              )
            })
          ) : (
            <div className="noData">暂无数据</div>
          )}
        </div>
        <div className="skills">可选配技能</div>
        <div className="common-skills">
          {noSkills.length > 0 ? (
            noSkills.map((item, index) => {
              return (
                <a href="#" key={item.id}>
                  <Badge
                    count={
                      <>
                        <img
                          src={addIcon}
                          alt=""
                          className="skill-badge"
                          onClick={(e) => {
                            e.preventDefault()
                            e.stopPropagation()
                            handleAddSkill(item)
                          }}
                        />
                      </>
                    }
                  >
                    <>
                      <div className="skill-item">
                        <div>
                          <ImgIcon src={images[item.fileId]} />
                        </div>
                        <div className="skill-item-title">{item.name}</div>
                      </div>
                      {renderAction(item)}
                    </>
                  </Badge>{' '}
                </a>
              )
            })
          ) : (
            <div className="noData">暂无数据</div>
          )}
        </div>
      </div>
    )
  }

  // 关闭新建技能弹窗并重置表单
  const handleCancel = () => {
    setOpen(false)
    setFileId('')
    setTempFileId(getRandomIcon())
    // 不要在这里重置 record，因为我们需要保持最新的数据
    // 但我们需要在取消时清除 record
    setRecord(null)
    form.resetFields()
  }
  const uploadButton = (
    <button
      style={{ border: 0, background: 'none', cursor: 'pointer' }}
      type="button"
      onClick={() => setOpenIcon(true)}
    >
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>技能图像</div>
    </button>
  )

  // 提交新建技能表单
  const onFinish = async (values: SaveUserSkillsParams) => {
    if (fileId === '') {
      message.error('请选择技能图像')
      return
    }
    setLoading(true)
    const isEdit = !!record?.id
    const params = {
      ...values,
      allowUploadFile: values.allowUploadFile === '1' ? true : false,
      isShowInBar: values.isShowInBar === true ? true : false,
      createUserId: id,
      fileId: fileId,
      isConfig: isEdit ? record.isConfig : 0,
      status: isEdit ? record.status : 0,
      id: record?.id, // 确保ID也被传递
    }
    if (isEdit) {
      params.id = record.id
    }
    try {
      const response = await saveUserSkills({ skillConfigInfos: [params] })
      // 确保响应是成功的
      if (
        !response ||
        typeof response !== 'object' ||
        'code' in response === false
      ) {
        throw new Error('无效的API响应')
      }

      if (response.code !== '0') {
        throw new Error(response.msg || '获取配置失败')
      }
      if (response.code === '0') {
        message.success(isEdit ? '修改成功' : '新建成功')
        if (isEdit) {
          if (record.isConfig === 1) {
            setHasSkills((prev) =>
              prev.map((item) => (item.id === record.id ? params : item))
            )
          } else {
            setNoSkills((prev) =>
              prev.map((item) => (item.id === record.id ? params : item))
            )
          }
          // 确保 record 状态是最新的
          setRecord(params)
        } else {
          setNoSkills((prev) => [...prev, response.resultData[0]])
        }

        handleCancel()
      }
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open && record?.id) {
      setFileId(record.fileId)
      form.setFieldsValue({
        ...record,
        allowUploadFile: record.allowUploadFile ? '1' : '2',
        isShowInBar: record.isShowInBar !== undefined ? record.isShowInBar : false,
      })
    } else if (open && !record?.id) {
      // 新建技能时的处理
      if (!fileId) {
        setFileId(getRandomIcon())
      }
      form.setFieldsValue({
        allowUploadFile: '2',
        isShowInBar: false,
      })
    }
  }, [open, record, form, fileId])

  // 应用已选技能配置
  const handleApply = async () => {
    setLoading(true)
    const data = hasSkills.map((item) => ({
      ...item,
      isConfig: 1,
    }))
    const noData = noSkills.map((item) => ({
      ...item,
      isConfig: 0,
    }))
    try {
      const response = await saveUserSkills({
        skillConfigInfos: data.concat(noData),
      })
      // 确保响应是成功的
      if (
        !response ||
        typeof response !== 'object' ||
        'code' in response === false
      ) {
        throw new Error('无效的API响应')
      }

      if (response.code !== '0') {
        throw new Error(response.msg || '获取配置失败')
      }
      if (response.code === '0') {
        message.success('应用成功！')
      }
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }

  // 取消图标选择
  const handleIconCancel = () => {
    setOpenIcon(false)
    setTempFileId(getRandomIcon())
  }

  // 确认选择的图标
  const confirmIcon = () => {
    if (tempFileId === '') {
      message.error('请选择技能图像')
      return
    }
    setFileId(tempFileId)
    setOpenIcon(false)
  }

  // 处理图标选择
  const handleIconSelect = (item) => {
    setTempFileId(item)
  }

  // 处理prompt优化请求
  const handlePrompt = async () => {
    setPromptLoading(true)
    const question = form.getFieldValue('prompt')
    if (!question) {
      setPromptLoading(false)
      message.error('请先输入技能prompt')
      return
    }
    try {
      const response: any = await optimizePrompt({
        question,
        stream: false,
        model: 'ht::saas-deepseek-v3',
      })
      if (response?.choices?.[0]?.message?.content) {
        form.setFieldsValue({
          prompt: response?.choices[0]?.message?.content,
        })
      } else if (response?.resultData?.choices?.[0]?.message?.content) {
        form.setFieldsValue({
          prompt: response?.resultData?.choices[0]?.message?.content,
        })
      } else {
        message.error('请求接口失败')
      }
    } catch (e) {
      console.log(e)
      message.error('请求接口失败')
    } finally {
      setPromptLoading(false)
    }
  }

  // 处理删除技能
  const handleDelete = (item) => {
    setDeleteOpen(true)
    setRecord(item)
  }

  //确认删除
  const confirmOk = async () => {
    setLoading(true)
    try {
      const response = await deleteUserKills({ id: record.id })
      // 确保响应是成功的
      if (
        !response ||
        typeof response !== 'object' ||
        'code' in response === false
      ) {
        throw new Error('无效的API响应')
      }

      if (response.code !== '0') {
        throw new Error(response.msg || '获取配置失败')
      }
      if (response.code === '0') {
        message.success('删除成功')
        if (record.isConfig === 1) {
          setHasSkills((prev) => prev.filter((item) => item.id !== record.id))
        } else {
          setNoSkills((prev) => prev.filter((item) => item.id !== record.id))
        }
        handleDeleteCancel()
      }
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }

  // 取消删除
  const handleDeleteCancel = () => {
    setDeleteOpen(false)
    setRecord(null)
  }
  // 处理编辑技能
  const handleEdit = (item) => {
    // 查找最新的技能信息
    let latestItem = item;
    if (item.isConfig === 1) {
      // 在已选技能中查找最新信息
      const updatedItem = hasSkills.find((skill) => skill.id === item.id);
      if (updatedItem) {
        latestItem = updatedItem;
      }
    } else {
      // 在可选技能中查找最新信息
      const updatedItem = noSkills.find((skill) => skill.id === item.id);
      if (updatedItem) {
        latestItem = updatedItem;
      }
    }

    setRecord(latestItem)
    setOpen(true)
  }

  // 渲染技能操作按钮(删除/编辑)
  const renderAction = (item) => {
    return (
      <div className="skill-item-action">
        <Button
          style={{
            height: 16,
            borderRadius: 2,
            fontSize: 8,
            padding: '0 6px',
            width: 'calc((100% - 4px)/2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onClick={() => handleDelete(item)}
        >
          删除
        </Button>
        <Button
          style={{
            height: 16,
            borderRadius: 2,
            background: '#1677FF',
            color: '#fff',
            fontSize: 8,
            padding: '0 6px',
            width: 'calc((100% - 4px)/2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onClick={() => handleEdit(item)}
        >
          编辑
        </Button>
      </div>
    )
  }

  return (
    <>
      <div className="settings-tab-page">
        <div className="settings-sidebar">
          <div className="sidebar-logo">
            <img src={logo} alt="logo" />
          </div>
          <div className="sidebar-nav">
            <div className="nav-item">
              <ImgIcon src={marketSkill} width={20} height={20} />
              技能市场
            </div>
          </div>
        </div>
        <div className="settings-main">
          <div className="main-content">
            {activeTab === 'market' && renderAccountTab()}
            {/* <div>
              <Button
                type="primary"
                style={{
                  height: 54,
                  borderRadius: 8,
                  backgroundColor: '#1677FF',
                  width: '100%',
                  fontSize: 24,
                  lineHeight: '22px',
                }}
                onClick={handleApply}
                loading={loading}
              >
                应用
              </Button>
            </div> */}
          </div>
        </div>
      </div>
      <Modal
        open={open}
        title={record?.id ? '编辑技能' : '新建技能'}
        onCancel={handleCancel}
        style={{ width: 640 }}
        footer={null}
        wrapClassName="skillModal"
      >
        <Upload
          name="file"
          listType="picture-card"
          className="avatar-uploader"
          showUploadList={false}
          beforeUpload={() => false}
          disabled
        >
          {fileId ? (
            <img
              src={images[fileId]}
              alt="avatar"
              style={{ width: 24, height: 24 }}
              onClick={() => {
                setOpenIcon(true)
                if (fileId) {
                  setTempFileId(fileId)
                }
              }}
            />
          ) : (
            uploadButton
          )}
        </Upload>
        <Form
          layout="vertical"
          form={form}
          className="formModal"
          onFinish={onFinish}
          initialValues={{
            allowUploadFile: record?.allowUploadFile
              ? record?.allowUploadFile
              : '2',
            isShowInBar: record?.isShowInBar !== undefined
              ? record?.isShowInBar
              : false,
          }}
          requiredMark={false}
        >
          <Form.Item
            name="name"
            label="技能名字"
            rules={[{ required: true, message: '请输入技能名字' }]}
          >
            <Input
              placeholder="请输入技能名字"
              style={{ width: 200, height: 40 }}
              size="large"
              maxLength={15}
            />
          </Form.Item>
          <Form.Item
            name="description"
            label="技能描述"
            rules={[{ required: true, message: '请输入技能描述' }]}
          >
            <Input
              placeholder="请输入技能描述"
              size="large"
              style={{ height: 40 }}
              maxLength={200}
            />
          </Form.Item>
          <Form.Item name="allowUploadFile" label="文件上传">
            <Radio.Group>
              <Radio value="1" style={{ flexDirection: 'row-reverse' }}>
                是否允许附件上传：是
              </Radio>
              <Radio value="2" style={{ flexDirection: 'row-reverse' }}>
                否
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item name="isShowInBar" label="是否在划词bar显示">
            <Radio.Group>
              <Radio value={true} style={{ flexDirection: 'row-reverse' }}>
                是
              </Radio>
              <Radio value={false} style={{ flexDirection: 'row-reverse' }}>
                否
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            name="prompt"
            label={
              <div className="skillPrompt">
                <span>技能prompt</span>
                {promptLoading ? (
                  <ImgIcon src={promptImg} width={14} height={14} />
                ) : (
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        cursor: 'pointer',
                      }}
                      onClick={handlePrompt}
                    >
                      <ImgIcon src={prompt} width={14} height={14} />
                      <span className="skillPrompt-desc">一键优化</span>
                    </span>
                    <span
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        cursor: 'pointer',
                      }}
                      onClick={() => window.open('http://prompt-optimizer.sit.saas.htsc/', '_blank')}
                    >
                      <ImgIcon src={prompt} width={14} height={14} />
                      <span className="skillPrompt-desc">提示词优化器</span>
                    </span>
                  </div>
                )}
              </div>
            }
            rules={[{ required: true, message: '请输入技能prompt' }]}
          >
            <Input.TextArea rows={5} placeholder="请输入技能prompt" />
          </Form.Item>
        </Form>
        <div className="modal-footer">
          <Button
            style={{
              height: 40,
              borderRadius: 8,
              fontSize: 14,
              lineHeight: '22px',
            }}
            onClick={handleCancel}
          >
            取消
          </Button>
          <Button
            style={{
              height: 40,
              borderRadius: 8,
              background: '#1677FF',
              color: '#fff',
              fontSize: 14,
              lineHeight: '22px',
            }}
            onClick={() => form.submit()}
            loading={loading}
          >
            确定
          </Button>
        </div>
      </Modal>
      <Modal
        open={openIcon}
        title={
          <>
            <LeftOutlined onClick={handleIconCancel} />
            <span style={{ paddingLeft: 24 }}>技能图像选择</span>
          </>
        }
        onCancel={handleIconCancel}
        style={{ width: 460 }}
        footer={null}
        wrapClassName="skillModal"
      >
        <div className="selectIcon">
          {tempFileId ? (
            <div className="selectIcon-icon">
              <ImgIcon className="icon-item" src={images[tempFileId]} />
            </div>
          ) : (
            <ImgIcon src={spaceIcon} width={58} height={59}></ImgIcon>
          )}
          <div className="selectIcon-title">技能图像</div>
        </div>
        <div className="iconList">
          {Object.entries(images)?.map(([iconName, url]) => (
            <ImgIcon
              className="icon-item"
              src={url}
              key={iconName}
              onClick={() => handleIconSelect(iconName)}
            />
          ))}
        </div>
        <div className="modal-footer">
          <Button
            style={{
              height: 40,
              borderRadius: 8,
              fontSize: 14,
              lineHeight: '22px',
            }}
            onClick={handleIconCancel}
          >
            取消
          </Button>
          <Button
            style={{
              height: 40,
              borderRadius: 8,
              background: '#1677FF',
              color: '#fff',
              fontSize: 14,
              lineHeight: '22px',
            }}
            onClick={confirmIcon}
            loading={loading}
          >
            确定
          </Button>
        </div>
      </Modal>
      <Modal
        title={
          <div className='delete-haeder'>
            <InfoCircleFilled
              style={{ color: '#FF4D4F', fontSize: 21 }}
            />
            <span className='delete-title'>是否删除该技能</span>
          </div>
        }
        open={deleteOpen}
        onOk={confirmOk}
        onCancel={handleDeleteCancel}
        cancelText="取消"
        confirmLoading={loading}
        okText="确定删除"
        okButtonProps={{
          style: {
            background: '#F5222D',
            color: '#fff',
            borderRadius: 4,
            fontSize: 12,
          },
        }}
        cancelButtonProps={{
          style: {
            fontSize: 12,
          },
        }}
      >
        <div className="delete-desc">
          删除后，该技能不可恢复，技能的使用历史也将被彻底删除。
        </div>
      </Modal>
    </>
  )
}
